/****************************************************************************
 *
 * (c) 2009-2020 QGROUNDCONTROL PROJECT <http://www.qgroundcontrol.org>
 *
 * QGroundControl is licensed according to the terms in the file
 * COPYING.md in the root of the source code directory.
 *
 ****************************************************************************/

import QtQuick                      2.11
import QtQuick.Controls             2.4
import QtLocation                   5.3
import QtPositioning                5.3
import QtQuick.Dialogs              1.2

import QGroundControl               1.0
import QGroundControl.Airspace      1.0
import QGroundControl.Controllers   1.0
import QGroundControl.Controls      1.0
import QGroundControl.FlightDisplay 1.0
import QGroundControl.FlightMap     1.0
import QGroundControl.Palette       1.0
import QGroundControl.ScreenTools   1.0
import QGroundControl.Vehicle       1.0

FlightMap {
    id:                         _root
    allowGCSLocationCenter:     true
    allowVehicleLocationCenter: !_keepVehicleCentered
    planView:                   false
    zoomLevel:                  QGroundControl.flightMapZoom
    center:                     QGroundControl.flightMapPosition

    property Item pipState: _pipState
    QGCPipState {
        id:         _pipState
        pipOverlay: _pipOverlay
        isDark:     _isFullWindowItemDark
    }

    property var    rightPanelWidth
    property var    planMasterController
    property bool   pipMode:                    false   // true: map is shown in a small pip mode
    property var    toolInsets                          // Insets for the center viewport area

    property var    _activeVehicle:             QGroundControl.multiVehicleManager.activeVehicle
    property var    _planMasterController:      planMasterController
    property var    _geoFenceController:        planMasterController.geoFenceController
    property var    _rallyPointController:      planMasterController.rallyPointController
    property var    _activeVehicleCoordinate:   _activeVehicle ? _activeVehicle.coordinate : QtPositioning.coordinate()
    property real   _toolButtonTopMargin:       parent.height - mainWindow.height + (ScreenTools.defaultFontPixelHeight / 2)
    property real   _toolsMargin:               ScreenTools.defaultFontPixelWidth * 0.75
    property bool   _airspaceEnabled:           QGroundControl.airmapSupported ? (QGroundControl.settingsManager.airMapSettings.enableAirMap.rawValue && QGroundControl.airspaceManager.connected): false
    property var    _flyViewSettings:           QGroundControl.settingsManager.flyViewSettings
    property bool   _keepMapCenteredOnVehicle:  _flyViewSettings.keepMapCenteredOnVehicle.rawValue

    property bool   _disableVehicleTracking:    false
    property bool   _keepVehicleCentered:       pipMode ? true : false
    property bool   _saveZoomLevelSetting:      true

    // note-zshun: 地图测量工具相关属性
    property var    _measureStartCoord:         null
    property var    _measureEndCoord:           null
    property bool   _measureFirstPointSet:      false

    function updateAirspace(reset) {
        if(_airspaceEnabled) {
            var coordinateNW = _root.toCoordinate(Qt.point(0,0), false /* clipToViewPort */)
            var coordinateSE = _root.toCoordinate(Qt.point(width,height), false /* clipToViewPort */)
            if(coordinateNW.isValid && coordinateSE.isValid) {
                QGroundControl.airspaceManager.setROI(coordinateNW, coordinateSE, false /*planView*/, reset)
            }
        }
    }

    function _adjustMapZoomForPipMode() {
        _saveZoomLevelSetting = false
        if (pipMode) {
            if (QGroundControl.flightMapZoom > 3) {
                zoomLevel = QGroundControl.flightMapZoom - 3
            }
        } else {
            zoomLevel = QGroundControl.flightMapZoom
        }
        _saveZoomLevelSetting = true
    }

    // note-zshun: 处理测量工具的点击事件
    function handleMeasureClick(coordinate) {
        if (!_measureFirstPointSet) {
            // 设置第一个点
            _measureStartCoord = coordinate
            _measureFirstPointSet = true
            _measureEndCoord = null
            console.log("设置起点: " + coordinate.latitude.toFixed(6) + ", " + coordinate.longitude.toFixed(6))
        } else {
            // 设置第二个点并计算距离
            _measureEndCoord = coordinate
            var distance = _measureStartCoord.distanceTo(_measureEndCoord)

            // note-zshun: 详细的距离计算信息
            console.log("=== 距离测量详情 ===")
            console.log("起点: " + _measureStartCoord.latitude.toFixed(8) + "°, " + _measureStartCoord.longitude.toFixed(8) + "°")
            console.log("终点: " + _measureEndCoord.latitude.toFixed(8) + "°, " + _measureEndCoord.longitude.toFixed(8) + "°")
            console.log("测量距离: " + distance.toFixed(3) + " 米")
            console.log("精度: ±1-3米 (使用椭球体模型)")

            // 计算坐标差值用于验证
            var latDiff = Math.abs(_measureEndCoord.latitude - _measureStartCoord.latitude)
            var lonDiff = Math.abs(_measureEndCoord.longitude - _measureStartCoord.longitude)
            console.log("纬度差: " + latDiff.toFixed(8) + "° (" + (latDiff * 111000).toFixed(2) + "m)")
            console.log("经度差: " + lonDiff.toFixed(8) + "° (" + (lonDiff * 111000 * Math.cos(_measureStartCoord.latitude * Math.PI/180)).toFixed(2) + "m)")
            console.log("==================")
            console.log("右键点击可清除测量结果，开始新的测量")
        }
    }

    // note-zshun: 处理右键点击，清除测量结果
    function handleMeasureRightClick() {
        if (QGroundControl.mapMeasureToolActive) {
            _measureFirstPointSet = false
            _measureStartCoord = null
            _measureEndCoord = null
            console.log("清除测量结果，准备新的测量")
        }
    }

    // note-zshun: 监听测量工具状态变化
    Connections {
        target: QGroundControl
        onMapMeasureToolActiveChanged: {
            if (!QGroundControl.mapMeasureToolActive) {
                // 工具关闭时重置状态
                _measureFirstPointSet = false
                _measureStartCoord = null
                _measureEndCoord = null
            }
        }
    }

    onPipModeChanged: _adjustMapZoomForPipMode()

    onVisibleChanged: {
        if (visible) {
            // Synchronize center position with Plan View
            center = QGroundControl.flightMapPosition
        }
    }

    onZoomLevelChanged: {
        if (_saveZoomLevelSetting) {
            QGroundControl.flightMapZoom = zoomLevel
            updateAirspace(false)
        }
    }
    onCenterChanged: {
        QGroundControl.flightMapPosition = center
        updateAirspace(false)
    }

    on_AirspaceEnabledChanged: {
        updateAirspace(true)
    }

    // We track whether the user has panned or not to correctly handle automatic map positioning
    Connections {
        target: gesture

        function onPanStarted() {       _disableVehicleTracking = true }
        function onFlickStarted() {     _disableVehicleTracking = true }
        function onPanFinished() {      panRecenterTimer.restart() }
        function onFlickFinished() {    panRecenterTimer.restart() }
    }

    function pointInRect(point, rect) {
        return point.x > rect.x &&
                point.x < rect.x + rect.width &&
                point.y > rect.y &&
                point.y < rect.y + rect.height;
    }

    property real _animatedLatitudeStart
    property real _animatedLatitudeStop
    property real _animatedLongitudeStart
    property real _animatedLongitudeStop
    property real animatedLatitude
    property real animatedLongitude

    onAnimatedLatitudeChanged: _root.center = QtPositioning.coordinate(animatedLatitude, animatedLongitude)
    onAnimatedLongitudeChanged: _root.center = QtPositioning.coordinate(animatedLatitude, animatedLongitude)

    NumberAnimation on animatedLatitude { id: animateLat; from: _animatedLatitudeStart; to: _animatedLatitudeStop; duration: 1000 }
    NumberAnimation on animatedLongitude { id: animateLong; from: _animatedLongitudeStart; to: _animatedLongitudeStop; duration: 1000 }

    function animatedMapRecenter(fromCoord, toCoord) {
        _animatedLatitudeStart = fromCoord.latitude
        _animatedLongitudeStart = fromCoord.longitude
        _animatedLatitudeStop = toCoord.latitude
        _animatedLongitudeStop = toCoord.longitude
        animateLat.start()
        animateLong.start()
    }

    function _insetRect() {
        return Qt.rect(toolInsets.leftEdgeCenterInset,
                       toolInsets.topEdgeCenterInset,
                       _root.width - toolInsets.leftEdgeCenterInset - toolInsets.rightEdgeCenterInset,
                       _root.height - toolInsets.topEdgeCenterInset - toolInsets.bottomEdgeCenterInset)
    }

    function recenterNeeded() {
        var vehiclePoint = _root.fromCoordinate(_activeVehicleCoordinate, false /* clipToViewport */)
        var insetRect = _insetRect()
        return !pointInRect(vehiclePoint, insetRect)
    }

    function updateMapToVehiclePosition() {
        if (animateLat.running || animateLong.running) {
            return
        }
        // We let FlightMap handle first vehicle position
        if (!_keepMapCenteredOnVehicle && firstVehiclePositionReceived && _activeVehicleCoordinate.isValid && !_disableVehicleTracking) {
            if (_keepVehicleCentered) {
                _root.center = _activeVehicleCoordinate
            } else {
                if (firstVehiclePositionReceived && recenterNeeded()) {
                    // Move the map such that the vehicle is centered within the inset area
                    var vehiclePoint = _root.fromCoordinate(_activeVehicleCoordinate, false /* clipToViewport */)
                    var insetRect = _insetRect()
                    var centerInsetPoint = Qt.point(insetRect.x + insetRect.width / 2, insetRect.y + insetRect.height / 2)
                    var centerOffset = Qt.point((_root.width / 2) - centerInsetPoint.x, (_root.height / 2) - centerInsetPoint.y)
                    var vehicleOffsetPoint = Qt.point(vehiclePoint.x + centerOffset.x, vehiclePoint.y + centerOffset.y)
                    var vehicleOffsetCoord = _root.toCoordinate(vehicleOffsetPoint, false /* clipToViewport */)
                    animatedMapRecenter(_root.center, vehicleOffsetCoord)
                }
            }
        }
    }

    on_ActiveVehicleCoordinateChanged: {
        if (_keepMapCenteredOnVehicle && _activeVehicleCoordinate.isValid && !_disableVehicleTracking) {
            _root.center = _activeVehicleCoordinate
        }
    }

    Timer {
        id:         panRecenterTimer
        interval:   10000
        running:    false
        onTriggered: {
            _disableVehicleTracking = false
            updateMapToVehiclePosition()
        }
    }

    Timer {
        interval:       500
        running:        true
        repeat:         true
        onTriggered:    updateMapToVehiclePosition()
    }

    QGCMapPalette { id: mapPal; lightColors: isSatelliteMap }

    Connections {
        target:                 _missionController
        ignoreUnknownSignals:   true
        function onNewItemsFromVehicle() {
            var visualItems = _missionController.visualItems
            if (visualItems && visualItems.count !== 1) {
                mapFitFunctions.fitMapViewportToMissionItems()
                firstVehiclePositionReceived = true
            }
        }
    }

    MapFitFunctions {
        id:                         mapFitFunctions // The name for this id cannot be changed without breaking references outside of this code. Beware!
        map:                        _root
        usePlannedHomePosition:     false
        planMasterController:       _planMasterController
    }

    ObstacleDistanceOverlayMap {
        id: obstacleDistance
        showText: !pipMode
    }

    // Add trajectory lines to the map
    MapPolyline {
        id:         trajectoryPolyline
        line.width: 3
        line.color: "red"
        z:          QGroundControl.zOrderTrajectoryLines
        visible:    !pipMode

        Connections {
            target:                 QGroundControl.multiVehicleManager
            function onActiveVehicleChanged(activeVehicle) {
                trajectoryPolyline.path = _activeVehicle ? _activeVehicle.trajectoryPoints.list() : []
            }
        }

        Connections {
            target:                 _activeVehicle ? _activeVehicle.trajectoryPoints : null
            onPointAdded:           trajectoryPolyline.addCoordinate(coordinate)
            onUpdateLastPoint:      trajectoryPolyline.replaceCoordinate(trajectoryPolyline.pathLength() - 1, coordinate)
            onPointsCleared:        trajectoryPolyline.path = []
        }
    }

    // Add the vehicles to the map
    MapItemView {
        model: QGroundControl.multiVehicleManager.vehicles
        delegate: VehicleMapItem {
            vehicle:        object
            coordinate:     object.coordinate
            map:            _root
            size:           pipMode ? ScreenTools.defaultFontPixelHeight : ScreenTools.defaultFontPixelHeight * 3
            z:              QGroundControl.zOrderVehicles
        }
    }
    // Add distance sensor view
    MapItemView{
        model: QGroundControl.multiVehicleManager.vehicles
        delegate: ProximityRadarMapView {
            vehicle:        object
            coordinate:     object.coordinate
            map:            _root
            z:              QGroundControl.zOrderVehicles
        }
    }
    // Add ADSB vehicles to the map
    MapItemView {
        model: QGroundControl.adsbVehicleManager.adsbVehicles
        delegate: VehicleMapItem {
            coordinate:     object.coordinate
            altitude:       object.altitude
            callsign:       object.callsign
            heading:        object.heading
            alert:          object.alert
            map:            _root
            z:              QGroundControl.zOrderVehicles
        }
    }

    // Add the items associated with each vehicles flight plan to the map
    Repeater {
        model: QGroundControl.multiVehicleManager.vehicles

        PlanMapItems {
            map:                    _root
            largeMapView:           !pipMode
            planMasterController:   masterController
            vehicle:                _vehicle

            property var _vehicle: object

            PlanMasterController {
                id: masterController
                Component.onCompleted: startStaticActiveVehicle(object)
            }
        }
    }

    MapItemView {
        model: pipMode ? undefined : _missionController.directionArrows

        delegate: MapLineArrow {
            fromCoord:      object ? object.coordinate1 : undefined
            toCoord:        object ? object.coordinate2 : undefined
            arrowPosition:  2
            z:              QGroundControl.zOrderWaypointLines
        }
    }

    // Allow custom builds to add map items
    CustomMapItems {
        map:            _root
        largeMapView:   !pipMode
    }

    GeoFenceMapVisuals {
        map:                    _root
        myGeoFenceController:   _geoFenceController
        interactive:            false
        planView:               false
        homePosition:           _activeVehicle && _activeVehicle.homePosition.isValid ? _activeVehicle.homePosition :  QtPositioning.coordinate()
    }

    // Rally points on map
    MapItemView {
        model: _rallyPointController.points

        delegate: MapQuickItem {
            id:             itemIndicator
            anchorPoint.x:  sourceItem.anchorPointX
            anchorPoint.y:  sourceItem.anchorPointY
            coordinate:     object.coordinate
            z:              QGroundControl.zOrderMapItems

            sourceItem: MissionItemIndexLabel {
                id:         itemIndexLabel
                label:      qsTr("R", "rally point map item label")
            }
        }
    }

    // Camera trigger points
    MapItemView {
        model: _activeVehicle ? _activeVehicle.cameraTriggerPoints : 0

        delegate: CameraTriggerIndicator {
            coordinate:     object.coordinate
            z:              QGroundControl.zOrderTopMost
        }
    }

    // GoTo Location visuals
    MapQuickItem {
        id:             gotoLocationItem
        visible:        false
        z:              QGroundControl.zOrderMapItems
        anchorPoint.x:  sourceItem.anchorPointX
        anchorPoint.y:  sourceItem.anchorPointY
        sourceItem: MissionItemIndexLabel {
            checked:    true
            index:      -1
            label:      qsTr("Go here", "Go to location waypoint")
        }

        property bool inGotoFlightMode: _activeVehicle ? _activeVehicle.flightMode === _activeVehicle.gotoFlightMode : false

        onInGotoFlightModeChanged: {
            if (!inGotoFlightMode && gotoLocationItem.visible) {
                // Hide goto indicator when vehicle falls out of guided mode
                gotoLocationItem.visible = false
            }
        }

        Connections {
            target: QGroundControl.multiVehicleManager
            function onActiveVehicleChanged(activeVehicle) {
                if (!activeVehicle) {
                    gotoLocationItem.visible = false
                }
            }
        }

        function show(coord) {
            gotoLocationItem.coordinate = coord
            gotoLocationItem.visible = true
        }

        function hide() {
            gotoLocationItem.visible = false
        }

        function actionConfirmed() {
            // We leave the indicator visible. The handling for onInGuidedModeChanged will hide it.
        }

        function actionCancelled() {
            hide()
        }
    }

    // Orbit editing visuals
    QGCMapCircleVisuals {
        id:             orbitMapCircle
        mapControl:     parent
        mapCircle:      _mapCircle
        visible:        false

        property alias center:              _mapCircle.center
        property alias clockwiseRotation:   _mapCircle.clockwiseRotation
        readonly property real defaultRadius: 30

        Connections {
            target: QGroundControl.multiVehicleManager
            function onActiveVehicleChanged(activeVehicle) {
                if (!activeVehicle) {
                    orbitMapCircle.visible = false
                }
            }
        }

        function show(coord) {
            _mapCircle.radius.rawValue = defaultRadius
            orbitMapCircle.center = coord
            orbitMapCircle.visible = true
        }

        function hide() {
            orbitMapCircle.visible = false
        }

        function actionConfirmed() {
            // Live orbit status is handled by telemetry so we hide here and telemetry will show again.
            hide()
        }

        function actionCancelled() {
            hide()
        }

        function radius() {
            return _mapCircle.radius.rawValue
        }

        Component.onCompleted: globals.guidedControllerFlyView.orbitMapCircle = orbitMapCircle

        QGCMapCircle {
            id:                 _mapCircle
            interactive:        true
            radius.rawValue:    30
            showRotation:       true
            clockwiseRotation:  true
        }
    }

    // ROI Location visuals
    MapQuickItem {
        id:             roiLocationItem
        visible:        _activeVehicle && _activeVehicle.isROIEnabled
        z:              QGroundControl.zOrderMapItems
        anchorPoint.x:  sourceItem.anchorPointX
        anchorPoint.y:  sourceItem.anchorPointY
        sourceItem: MissionItemIndexLabel {
            checked:    true
            index:      -1
            label:      qsTr("ROI here", "Make this a Region Of Interest")
        }

        //-- Visibilty controlled by actual state
        function show(coord) {
            roiLocationItem.coordinate = coord
        }

        function hide() {
        }

        function actionConfirmed() {
        }

        function actionCancelled() {
        }
    }

    // Orbit telemetry visuals
    QGCMapCircleVisuals {
        id:             orbitTelemetryCircle
        mapControl:     parent
        mapCircle:      _activeVehicle ? _activeVehicle.orbitMapCircle : null
        visible:        _activeVehicle ? _activeVehicle.orbitActive : false
    }

    MapQuickItem {
        id:             orbitCenterIndicator
        anchorPoint.x:  sourceItem.anchorPointX
        anchorPoint.y:  sourceItem.anchorPointY
        coordinate:     _activeVehicle ? _activeVehicle.orbitMapCircle.center : QtPositioning.coordinate()
        visible:        orbitTelemetryCircle.visible

        sourceItem: MissionItemIndexLabel {
            checked:    true
            index:      -1
            label:      qsTr("Orbit", "Orbit waypoint")
        }
    }

    // Handle guided mode clicks
    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        // note-zshun: 只有在测量工具激活时才提高z-order
        z: QGroundControl.mapMeasureToolActive ? QGroundControl.zOrderTopMost + 1 : 0

        QGCMenu {
            id: clickMenu
            property var coord
            property var mousePos
            QGCMenuItem {
                text:           qsTr("前往位置")  // note-zshun，中文化菜单项
                visible:        globals.guidedControllerFlyView.showGotoLocation

                onTriggered: {
                    // note-zshun，弹出GPS坐标编辑对话框，在鼠标点击位置附近显示
                    console.log("FlyViewMap: menu triggered, coord:", clickMenu.coord)

                    // 确保gotoLocationItem处于正确状态
                    gotoLocationItem.hide()
                    gotoLocationItem.show(clickMenu.coord)

                    gotoLocationDialog.coordinate = clickMenu.coord

                    // 计算对话框显示位置（在鼠标点击位置右下方偏移）
                    var dialogX = Math.min(clickMenu.mousePos.x + 20, _root.width - gotoLocationDialog.width - 20)
                    var dialogY = Math.min(clickMenu.mousePos.y + 20, _root.height - gotoLocationDialog.height - 20)

                    // 确保对话框不会超出屏幕边界
                    dialogX = Math.max(20, dialogX)
                    dialogY = Math.max(20, dialogY)

                    gotoLocationDialog.x = dialogX
                    gotoLocationDialog.y = dialogY
                    gotoLocationDialog.open()
                }
            }
            QGCMenuItem {
                text:           qsTr("Orbit at location")
                visible:        globals.guidedControllerFlyView.showOrbit

                onTriggered: {
                    orbitMapCircle.show(clickMenu.coord)
                    globals.guidedControllerFlyView.confirmAction(globals.guidedControllerFlyView.actionOrbit, clickMenu.coord, orbitMapCircle)
                }
            }
            QGCMenuItem {
                text:           qsTr("ROI at location")
                visible:        globals.guidedControllerFlyView.showROI

                onTriggered: {
                    roiLocationItem.show(clickMenu.coord)
                    globals.guidedControllerFlyView.confirmAction(globals.guidedControllerFlyView.actionROI, clickMenu.coord, roiLocationItem)
                }
            }
        }

        onClicked: {
            // note-zshun: 处理地图测量工具的点击事件
            if (QGroundControl.mapMeasureToolActive) {
                var clickCoord = _root.toCoordinate(Qt.point(mouse.x, mouse.y), false /* clipToViewPort */)
                if (mouse.button === Qt.LeftButton) {
                    console.log("测量模式下左键点击")
                    handleMeasureClick(clickCoord)
                } else if (mouse.button === Qt.RightButton) {
                    console.log("测量模式下右键点击，清除测量")
                    handleMeasureRightClick()
                }
                mouse.accepted = true
                return
            }

            // 原有的guided模式处理 - note-zshun，修复引导模式地图点击问题
            if (!globals.guidedControllerFlyView.guidedUIVisible && globals.guidedControllerFlyView.showGotoLocation) {
                orbitMapCircle.hide()
                gotoLocationItem.hide()
                var clickCoord = _root.toCoordinate(Qt.point(mouse.x, mouse.y), false /* clipToViewPort */)
                clickMenu.coord = clickCoord
                clickMenu.mousePos = Qt.point(mouse.x, mouse.y)  // note-zshun，保存鼠标位置
                clickMenu.popup()
            }
        }
    }



    // note-zshun，GPS坐标编辑对话框
    GuidedGotoLocationDialog {
        id: gotoLocationDialog
        guidedController: globals.guidedControllerFlyView
        mapIndicator: gotoLocationItem

        onCoordinateUpdated: {
            gotoLocationItem.show(coordinate)
        }
    }

    // Airspace overlap support
    MapItemView {
        model:              _airspaceEnabled && QGroundControl.settingsManager.airMapSettings.enableAirspace && QGroundControl.airspaceManager.airspaceVisible ? QGroundControl.airspaceManager.airspaces.circles : []
        delegate: MapCircle {
            center:         object.center
            radius:         object.radius
            color:          object.color
            border.color:   object.lineColor
            border.width:   object.lineWidth
        }
    }

    MapItemView {
        model:              _airspaceEnabled && QGroundControl.settingsManager.airMapSettings.enableAirspace && QGroundControl.airspaceManager.airspaceVisible ? QGroundControl.airspaceManager.airspaces.polygons : []
        delegate: MapPolygon {
            path:           object.polygon
            color:          object.color
            border.color:   object.lineColor
            border.width:   object.lineWidth
        }
    }

    MapScale {
        id:                 mapScale
        anchors.margins:    _toolsMargin
        anchors.left:       parent.left
        anchors.top:        parent.top
        mapControl:         _root
        buttonsOnLeft:      false
        visible:            !ScreenTools.isTinyScreen && QGroundControl.corePlugin.options.flyView.showMapScale && mapControl.pipState.state === mapControl.pipState.windowState

        property real centerInset: visible ? parent.height - y : 0
    }

    // note-zshun: 地图测量工具可视化组件

    // 第一个测量点 - 定位图标样式
    MapQuickItem {
        id: measureStartPoint
        visible: QGroundControl.mapMeasureToolActive && _measureStartCoord
        coordinate: _measureStartCoord ? _measureStartCoord : QtPositioning.coordinate()
        anchorPoint.x: sourceItem.width / 2
        anchorPoint.y: sourceItem.height

        sourceItem: Item {
            width: 48
            height: 64

            // 定位图标主体
            Rectangle {
                width: 40
                height: 40
                radius: 20
                color: "#FF4444"
                border.color: "white"
                border.width: 5
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: parent.top

                // 内部小圆点
                Rectangle {
                    width: 16
                    height: 16
                    radius: 8
                    color: "white"
                    anchors.centerIn: parent
                }
            }

            // 定位图标尖角
            Rectangle {
                width: 16
                height: 16
                color: "#FF4444"
                rotation: 45
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: parent.top
                anchors.topMargin: 32
            }

            // 标签
            Rectangle {
                color: "white"
                border.color: "#FF4444"
                border.width: 1
                radius: 3
                width: startText.width + 6
                height: startText.height + 2
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.bottom: parent.top
                anchors.bottomMargin: 2

                Text {
                    id: startText
                    text: "起点"
                    font.pixelSize: 14
                    font.bold: true
                    color: "#FF4444"
                    anchors.centerIn: parent
                }
            }
        }
    }

    // 第二个测量点 - 定位图标样式
    MapQuickItem {
        id: measureEndPoint
        visible: QGroundControl.mapMeasureToolActive && _measureEndCoord
        coordinate: _measureEndCoord ? _measureEndCoord : QtPositioning.coordinate()
        anchorPoint.x: sourceItem.width / 2
        anchorPoint.y: sourceItem.height

        sourceItem: Item {
            width: 48
            height: 64

            // 定位图标主体
            Rectangle {
                width: 40
                height: 40
                radius: 20
                color: "#44FF44"
                border.color: "white"
                border.width: 5
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: parent.top

                // 内部小圆点
                Rectangle {
                    width: 16
                    height: 16
                    radius: 8
                    color: "white"
                    anchors.centerIn: parent
                }
            }

            // 定位图标尖角
            Rectangle {
                width: 16
                height: 16
                color: "#44FF44"
                rotation: 45
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: parent.top
                anchors.topMargin: 32
            }

            // 标签
            Rectangle {
                color: "white"
                border.color: "#44FF44"
                border.width: 1
                radius: 3
                width: endText.width + 6
                height: endText.height + 2
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.bottom: parent.top
                anchors.bottomMargin: 2

                Text {
                    id: endText
                    text: "终点"
                    font.pixelSize: 14
                    font.bold: true
                    color: "#44FF44"
                    anchors.centerIn: parent
                }
            }
        }
    }

    // 测量线
    MapPolyline {
        id: measureLine
        visible: QGroundControl.mapMeasureToolActive && _measureStartCoord && _measureEndCoord
        line.width: 8
        line.color: "#FF6600"
        opacity: 0.8
        path: _measureStartCoord && _measureEndCoord ? [_measureStartCoord, _measureEndCoord] : []
    }

    // 测量线阴影效果
    MapPolyline {
        id: measureLineShadow
        visible: QGroundControl.mapMeasureToolActive && _measureStartCoord && _measureEndCoord
        line.width: 12
        line.color: "black"
        opacity: 0.3
        path: _measureStartCoord && _measureEndCoord ? [_measureStartCoord, _measureEndCoord] : []
        z: measureLine.z - 1
    }

    // 距离标签
    MapQuickItem {
        id: measureLabel
        visible: QGroundControl.mapMeasureToolActive && _measureStartCoord && _measureEndCoord
        coordinate: _measureStartCoord && _measureEndCoord ?
                   QtPositioning.coordinate(
                       (_measureStartCoord.latitude + _measureEndCoord.latitude) / 2,
                       (_measureStartCoord.longitude + _measureEndCoord.longitude) / 2
                   ) : QtPositioning.coordinate()
        anchorPoint.x: sourceItem.width / 2
        anchorPoint.y: sourceItem.height / 2

        sourceItem: Rectangle {
            color: "#FF6600"
            border.color: "white"
            border.width: 3
            radius: 12
            width: distanceText.width + 24
            height: distanceText.height + 16

            // 阴影效果
            Rectangle {
                color: "black"
                opacity: 0.4
                radius: 12
                width: parent.width + 2
                height: parent.height + 2
                anchors.centerIn: parent
                z: parent.z - 1
            }

            Text {
                id: distanceText
                anchors.centerIn: parent
                text: _measureStartCoord && _measureEndCoord ?
                     (_measureStartCoord.distanceTo(_measureEndCoord).toFixed(2) + " 米") : ""
                font.pixelSize: 20
                font.bold: true
                color: "white"
                style: Text.Outline
                styleColor: "black"
            }
        }
    }

}
