/****************************************************************************
 *
 * (c) 2009-2020 QGROUNDCONTROL PROJECT <http://www.qgroundcontrol.org>
 *
 * QGroundControl is licensed according to the terms in the file
 * COPYING.md in the root of the source code directory.
 *
 ****************************************************************************/

import QtQuick          2.12
import QtQuick.Controls 2.4
import QtQuick.Layouts  1.12

import QGroundControl               1.0
import QGroundControl.ScreenTools   1.0
import QGroundControl.Controls      1.0
import QGroundControl.Palette       1.0

Rectangle {
    id:                     _root
    width:                  Math.max(mainLayout.implicitWidth + (_margins * 2), ScreenTools.defaultFontPixelWidth * 25)  // note-zshun，减小对话框宽度
    height:                 Math.max(mainLayout.implicitHeight + (_margins * 2), ScreenTools.defaultFontPixelHeight * 6)   // note-zshun，减小对话框高度
    radius:                 ScreenTools.defaultFontPixelWidth / 2
    color:                  qgcPal.windowShadeLight
    visible:                false

    property var    guidedController
    property var    altitudeSlider
    property string title                                       // Currently unused
    property alias  message:            messageText.text
    property int    action
    property var    actionData
    property bool   hideTrigger:        false
    property var    mapIndicator
    property alias  optionText:         optionCheckBox.text
    property alias  optionChecked:      optionCheckBox.checked

    property real _margins:         ScreenTools.defaultFontPixelWidth / 2
    property bool _emergencyAction: action === guidedController.actionEmergencyStop

    Component.onCompleted: guidedController.confirmDialog = this

    onHideTriggerChanged: {
        if (hideTrigger) {
            confirmCancelled()
        }
    }

    function show(immediate) {
        if (immediate) {
            visible = true
        } else {
            // We delay showing the confirmation for a small amount in order for any other state
            // changes to propogate through the system. This way only the final state shows up.
            visibleTimer.restart()
        }
    }

    function confirmCancelled() {
        altitudeSlider.visible = false
        visible = false
        hideTrigger = false
        visibleTimer.stop()
        if (mapIndicator) {
            mapIndicator.actionCancelled()
            mapIndicator = undefined
        }
        // note-zshun: 确保UI状态重置，允许下次点击
        Qt.callLater(function() {
            guidedController.closeAll()
        })
    }

    Timer {
        id:             visibleTimer
        interval:       1000
        repeat:         false
        onTriggered:    visible = true
    }

    QGCPalette { id: qgcPal }

    ColumnLayout {
        id:                         mainLayout
        anchors.horizontalCenter:   parent.horizontalCenter
        spacing:                    _margins

        QGCLabel {
            id:                     messageText
            Layout.fillWidth:       true
            horizontalAlignment:    Text.AlignHCenter
            wrapMode:               Text.WordWrap
            font.pointSize:         ScreenTools.defaultFontPointSize  // note-zshun，设置合适的字体大小
        }

        QGCCheckBox {
            id:                 optionCheckBox
            Layout.alignment:   Qt.AlignHCenter
            text:               ""
            visible:            text !== ""
        }

        RowLayout {
            Layout.alignment:       Qt.AlignHCenter
            spacing:                ScreenTools.defaultFontPixelWidth

            SliderSwitch {
                id:                     slider
                confirmText:            qsTr("滑动确认")  // note-zshun，中文化
                Layout.minimumWidth:    Math.max(implicitWidth, ScreenTools.defaultFontPixelWidth * 20)  // note-zshun，减小滑块宽度
                fontPointSize:          ScreenTools.defaultFontPointSize  // note-zshun，设置合适的字体大小

                onAccept: {
                    _root.visible = false
                    var altitudeChange = 0
                    if (altitudeSlider.visible) {
                        altitudeChange = altitudeSlider.getAltitudeChangeValue()
                        altitudeSlider.visible = false
                    }
                    hideTrigger = false
                    guidedController.executeAction(_root.action, _root.actionData, altitudeChange, _root.optionChecked)
                    if (mapIndicator) {
                        mapIndicator.actionConfirmed()
                        mapIndicator = undefined
                    }
                    // note-zshun: 确保UI状态重置，允许下次点击
                    Qt.callLater(function() {
                        guidedController.closeAll()
                    })
                }
            }

            Rectangle {
                height: slider.height * 0.75
                width:  height
                radius: height / 2
                color:  qgcPal.primaryButton

                QGCColoredImage {
                    anchors.margins:    parent.height / 4
                    anchors.fill:       parent
                    source:             "/res/XDelete.svg"
                    fillMode:           Image.PreserveAspectFit
                    color:              qgcPal.text
                }

                QGCMouseArea {
                    fillItem:   parent
                    onClicked:  confirmCancelled()
                }
            }
        }
    }
}

