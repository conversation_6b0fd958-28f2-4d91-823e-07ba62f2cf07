/****************************************************************************
 *
 * (c) 2009-2020 QGROUNDCONTROL PROJECT <http://www.qgroundcontrol.org>
 *
 * QGroundControl is licensed according to the terms in the file
 * COPYING.md in the root of the source code directory.
 *
 ****************************************************************************/

import QtQuick          2.12
import QtQuick.Controls 2.4
import QtQuick.Layouts  1.11
import QtQuick.Dialogs  1.3
import QtPositioning    5.3

import QGroundControl               1.0
import QGroundControl.Controls      1.0
import QGroundControl.Palette       1.0
import QGroundControl.ScreenTools   1.0

// note-zshun，GPS坐标编辑对话框
Popup {
    id:         _root
    modal:      true
    focus:      true
    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside

    // 设置对话框大小
    width:      Math.max(450, mainContent.implicitWidth + 60)
    height:     Math.max(280, mainContent.implicitHeight + 60)

    property var coordinate: QtPositioning.coordinate()
    property var guidedController
    property var mapIndicator

    signal coordinateUpdated()

    property real _margins: ScreenTools.defaultFontPixelWidth

    QGCPalette { id: qgcPal; colorGroupEnabled: enabled }

    // 背景
    background: Rectangle {
        color: qgcPal.window
        border.color: qgcPal.text
        border.width: 1
        radius: ScreenTools.defaultFontPixelHeight * 0.5

        // 阴影效果
        Rectangle {
            anchors.fill: parent
            anchors.topMargin: 3
            anchors.leftMargin: 3
            color: "black"
            opacity: 0.2
            radius: parent.radius
            z: -1
        }
    }

    // 格式化坐标显示
    function formatCoordinate(coord) {
        if (!coord.isValid) {
            return qsTr("无效坐标")
        }
        
        var latDir = coord.latitude >= 0 ? "N" : "S"
        var lonDir = coord.longitude >= 0 ? "E" : "W"
        var latDeg = Math.abs(coord.latitude)
        var lonDeg = Math.abs(coord.longitude)
        
        return latDeg.toFixed(6) + "°" + latDir + ", " + lonDeg.toFixed(6) + "°" + lonDir
    }

    // 解析坐标输入
    function parseCoordinate(latText, lonText) {
        var lat = parseFloat(latText)
        var lon = parseFloat(lonText)
        
        if (isNaN(lat) || isNaN(lon)) {
            return QtPositioning.coordinate()
        }
        
        // 限制坐标范围
        lat = Math.max(-90, Math.min(90, lat))
        lon = Math.max(-180, Math.min(180, lon))
        
        return QtPositioning.coordinate(lat, lon)
    }

    function acceptAction() {
        var newCoord = parseCoordinate(latitudeField.text, longitudeField.text)
        if (newCoord.isValid) {
            coordinate = newCoord
            coordinateUpdated()
            // note-zshun，直接执行goto命令，简化流程
            console.log("GuidedGotoLocationDialog: 直接执行goto到坐标:", coordinate)
            if (guidedController && guidedController._activeVehicle) {
                guidedController._activeVehicle.guidedModeGotoLocation(coordinate)
                console.log("GuidedGotoLocationDialog: 已发送goto命令")
                if (mapIndicator) {
                    mapIndicator.actionConfirmed()
                }
            } else {
                console.log("GuidedGotoLocationDialog: guidedController或activeVehicle为空!")
            }
            close()
        } else {
            console.log("GuidedGotoLocationDialog: 无效坐标")
            close()
        }
    }

    function rejectAction() {
        if (mapIndicator) {
            mapIndicator.actionCancelled()
        }
        close()
    }

    // 键盘快捷键支持
    Keys.onPressed: {
        if (event.key === Qt.Key_Return || event.key === Qt.Key_Enter) {
            acceptAction()
            event.accepted = true
        } else if (event.key === Qt.Key_Escape) {
            rejectAction()
            event.accepted = true
        }
    }

    ColumnLayout {
        id: mainContent
        anchors.fill: parent
        anchors.margins: 20
        spacing: _margins

        // 标题
        QGCLabel {
            text: qsTr("前往位置")
            font.pointSize: ScreenTools.largeFontPointSize
            font.bold: true
            color: qgcPal.text
            Layout.alignment: Qt.AlignHCenter
        }

        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: qgcPal.text
            opacity: 0.3
        }

        QGCLabel {
            text: qsTr("点击位置坐标：")
            font.pointSize: ScreenTools.mediumFontPointSize
        }

        QGCLabel {
            text: formatCoordinate(coordinate)
            font.pointSize: ScreenTools.defaultFontPointSize
            color: qgcPal.warningText
        }

        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: qgcPal.text
            opacity: 0.3
        }

        QGCLabel {
            text: qsTr("自定义坐标（可选）：")
            font.pointSize: ScreenTools.mediumFontPointSize
        }

        GridLayout {
            columns: 2
            columnSpacing: _margins
            rowSpacing: _margins / 2

            QGCLabel {
                text: qsTr("纬度：")
            }

            QGCTextField {
                id: latitudeField
                Layout.preferredWidth: ScreenTools.defaultFontPixelWidth * 20
                placeholderText: qsTr("例：39.123456 (N为正，S为负)")
                text: coordinate.isValid ? coordinate.latitude.toFixed(6) : ""
                inputMethodHints: Qt.ImhFormattedNumbersOnly
            }

            QGCLabel {
                text: qsTr("经度：")
            }

            QGCTextField {
                id: longitudeField
                Layout.preferredWidth: ScreenTools.defaultFontPixelWidth * 20
                placeholderText: qsTr("例：116.123456 (E为正，W为负)")
                text: coordinate.isValid ? coordinate.longitude.toFixed(6) : ""
                inputMethodHints: Qt.ImhFormattedNumbersOnly
            }
        }



        QGCLabel {
            text: qsTr("点击确定后无人船将直接前往指定位置")
            font.pointSize: ScreenTools.smallFontPointSize
            color: qgcPal.text
            opacity: 0.8
            Layout.alignment: Qt.AlignHCenter
            Layout.topMargin: _margins
        }

        // 按钮行
        RowLayout {
            Layout.fillWidth: true
            Layout.topMargin: _margins / 2

            Item { Layout.fillWidth: true } // 弹簧

            QGCButton {
                text: qsTr("取消")
                onClicked: rejectAction()
            }

            QGCButton {
                text: qsTr("确定")
                primary: true
                onClicked: acceptAction()
            }
        }
    }
}
